/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable no-undef */

import { Page } from 'puppeteer';

import { CompanyInfo } from '../../types/scrapper';
import logger from '../utils/logger';
import { extractTeamInfo, isTeamInfoComplete, mergeTeamInfo } from './team-info';

/**
 * Interface for company about information
 * Extended from CompanyInfo but with stronger typing
 */
interface AboutInfo {
	companyDescription?: string;
	foundingInfo?: string;
	missionStatement?: string;
	companyValues?: string[];
	awards?: string[];
	industries?: string[];
	globalPresence?: boolean;
	officeLocations?: string[];
	certifications?: string[];
}

/**
 * Extracts about information from a webpage
 * @param page - Puppeteer page instance
 * @returns Promise containing company info
 */
export async function extractAboutInfo(page: Page): Promise<AboutInfo> {
	logger.info("Extracting about page information");

	try {
		// Using a string-based function to avoid any TypeScript transpilation issues in the browser context
		const result = await page.evaluate(`
      (function() {
        // Define our structure in plain JavaScript
        var aboutInfo = {
          companyDescription: undefined,
          foundingInfo: undefined,
          missionStatement: undefined,
          companyValues: undefined,
          awards: undefined,
          industries: undefined,
          globalPresence: undefined,
          officeLocations: [],
          certifications: undefined
        };

        // Helper function to check if a string contains a pattern
        function stringContains(text, pattern) {
          if (!text) return false;
          return text.toLowerCase().indexOf(pattern.toLowerCase()) !== -1;
        }

        // Helper function to check if an element matches a pattern
        function matchesPattern(element, pattern, attribute) {
          attribute = attribute || 'class';
          var attributeValue = element.getAttribute(attribute);
          return attributeValue ? stringContains(attributeValue, pattern) : false;
        }

        // Helper function to find elements by text content
        function getElementsByText(tag, text) {
          var elements = document.querySelectorAll(tag);
          return Array.from(elements).filter(function(element) {
            return element.textContent && element.textContent.toLowerCase().includes(text.toLowerCase());
          });
        }
        
        // Helper function to clean text
        function cleanText(text) {
          if (!text) return '';

          // Replace multiple spaces with a single space
          var cleaned = text.replace(/\\s+/g, ' ');

          // Replace weird characters that might be from HTML parsing
          cleaned = cleaned.replace(/\\/g, '');

          // Fix common artifacts from HTML extraction
          cleaned = cleaned.replace(/\\s+([.,;:!?])/g, '$1');
          cleaned = cleaned.replace(/\\s+/g, ' ');

          // Trim leading/trailing whitespace
          return cleaned.trim();
        }

        // Process all the text content from potential about sections to extract company description
        var allDescriptionText = '';

        // Look for specific about sections first
        var aboutSections = Array.from(document.querySelectorAll('*')).filter(function(el) {
          var id = el.id ? el.id.toLowerCase() : '';
          var className = el.className && typeof el.className === 'string' ? el.className.toLowerCase() : '';
          var dataAttr = el.getAttribute('data-section') || '';

          return id.includes('about') ||
                 className.includes('about') ||
                 dataAttr.includes('about') ||
                 (el.tagName === 'SECTION' && el.textContent && el.textContent.toLowerCase().includes('about us'));
        });
        
        // If we found specific about sections, use those first
        if (aboutSections.length > 0) {
          aboutSections.forEach(function(section) {
            if (section && section.textContent) {
              var text = section.textContent.trim();
              if (text.length > 50) { // Only consider substantial text
                allDescriptionText += text + ' ';
              }
            }
          });
        } else {
          // Fallback to looking for any substantial text sections
          var potentialAboutSections = document.querySelectorAll('section, div, article');
          potentialAboutSections.forEach(function(section) {
            if (section && section.textContent) {
              var text = section.textContent.trim();
              if (text.length > 50 &&
                  !section.querySelector('nav') && // Exclude navigation
                  !matchesPattern(section, 'footer') && // Exclude footers
                  !matchesPattern(section, 'header') && // Exclude headers
                  !matchesPattern(section, 'sidebar')) { // Exclude sidebars
                allDescriptionText += text + ' ';
              }
            }
          });
        }

        // Clean up description text
        if (allDescriptionText) {
          // Clean and format the description
          allDescriptionText = cleanText(allDescriptionText);
          
          // Set as company description if substantial
          if (allDescriptionText.length > 100) {
            aboutInfo.companyDescription = allDescriptionText.substring(0, 1000); // Limit to 1000 chars
          }
        }

        // Extract founding info
        try {
          // Simple text-based founding info extraction (avoiding regex entirely)
          // Check all text in the document
          var textElements = document.querySelectorAll('p, div, span, section, article');
          var foundingKeywords = ['founded in', 'established in', 'since', 'founded'];

          for (var i = 0; i < textElements.length; i++) {
            var element = textElements[i];
            if (element.textContent) {
              var text = element.textContent.trim().toLowerCase();

              // Try each founding keyword to find a year
              for (var j = 0; j < foundingKeywords.length; j++) {
                var keyword = foundingKeywords[j];
                var keywordIndex = text.indexOf(keyword);

                if (keywordIndex !== -1) {
                  // Look for a 4-digit year in the text near the keyword
                  // Check text before and after the keyword
                  var textToSearch = text.substring(Math.max(0, keywordIndex - 20),
                                                 Math.min(text.length, keywordIndex + keyword.length + 20));

                  // Find a 4-digit number in the text segment
                  var yearMatches = textToSearch.match(/\\d{4}/g);
                  if (yearMatches && yearMatches.length > 0) {
                    for (var k = 0; k < yearMatches.length; k++) {
                      var year = parseInt(yearMatches[k], 10);
                      if (year > 1900 && year <= new Date().getFullYear()) {
                        aboutInfo.foundingInfo = 'Founded in ' + year;
                        break;
                      }
                    }
                  }
                }

                if (aboutInfo.foundingInfo) break;
              }
              
              if (aboutInfo.foundingInfo) break;
            }
          }
        } catch (foundingError) {
          logger.error('Error extracting founding info:', foundingError);
        }
        
        // Extract mission statement
        try {
          // Look for mission statement sections
          const missionHeadings = getElementsByText('h1, h2, h3, h4, h5', 'mission');
          missionHeadings.push.apply(missionHeadings, getElementsByText('h1, h2, h3, h4, h5', 'purpose'));
          missionHeadings.push.apply(missionHeadings, getElementsByText('h1, h2, h3, h4, h5', 'vision'));
          
          for (let i = 0; i < missionHeadings.length; i++) {
            const heading = missionHeadings[i];
            let missionText = '';
            
            // Check the next element
            if (heading.nextElementSibling && heading.nextElementSibling.textContent) {
              missionText = heading.nextElementSibling.textContent.trim();
            }
            // If no text in next element, try parent's text excluding heading
            else if (heading.parentElement) {
              const parentText = heading.parentElement.textContent.trim();
              const headingText = heading.textContent.trim();
              
              if (parentText && headingText && parentText.includes(headingText)) {
                missionText = parentText.replace(headingText, '').trim();
              }
            }
            
            if (missionText && missionText.length > 20) {
              aboutInfo.missionStatement = missionText;
              break;
            }
          }
        } catch (missionError) {
          logger.error('Error extracting mission statement:', missionError);
        }
        
        // Extract company values
        try {
          const valuesHeadings = getElementsByText('h1, h2, h3, h4, h5', 'values');
          valuesHeadings.push.apply(valuesHeadings, getElementsByText('h1, h2, h3, h4, h5', 'principles'));
          valuesHeadings.push.apply(valuesHeadings, getElementsByText('h1, h2, h3, h4, h5', 'beliefs'));
          
          let valuesList = [];
          
          for (let i = 0; i < valuesHeadings.length; i++) {
            const heading = valuesHeadings[i];
            
            // Look for list items after the heading
            if (heading.nextElementSibling) {
              const listItems = heading.nextElementSibling.querySelectorAll('li');
              
              if (listItems && listItems.length > 0) {
                for (let j = 0; j < listItems.length; j++) {
                  const value = listItems[j].textContent.trim();
                  if (value && value.length > 0) {
                    valuesList.push(value);
                  }
                }
              }
            }
          }
          
          if (valuesList.length > 0) {
            aboutInfo.companyValues = valuesList;
          }
        } catch (valuesError) {
          logger.error('Error extracting company values:', valuesError);
        }
        
        // Look for office locations
        const addressElements = document.querySelectorAll('address');
        let officeLocations = [];
        
        // First try to find actual address elements
        if (addressElements && addressElements.length > 0) {
          for (let i = 0; i < addressElements.length; i++) {
            const addressText = cleanText(addressElements[i].textContent);
            if (addressText && addressText.length > 10) {
              officeLocations.push(addressText);
            }
          }
        }
        
        // If no address elements, look for divs/sections containing address-like content
        if (officeLocations.length === 0) {
          const locationKeywords = ['office', 'location', 'address', 'contact us', 'hq', 'headquarters'];
          
          // Find elements that might contain address information
          const locationElements = [];
          for (const keyword of locationKeywords) {
            const elements = getElementsByText('*', keyword);
            for (const el of elements) {
              locationElements.push(el);
            }
          }
          
          // Also look for elements with location-related classes or IDs
          const allElements = document.querySelectorAll('div, section, footer');
          for (let i = 0; i < allElements.length; i++) {
            const el = allElements[i];
            const id = el.id ? el.id.toLowerCase() : '';
            const className = el.className && typeof el.className === 'string' ? el.className.toLowerCase() : '';
            
            if (id.includes('location') || id.includes('address') || id.includes('contact') ||
                className.includes('location') || className.includes('address') || className.includes('contact')) {
              locationElements.push(el);
            }
          }
          
          // Extract address-like text from these elements
          for (const el of locationElements) {
            // Look for paragraphs within this element that might be addresses
            const paragraphs = el.querySelectorAll('p');
            let foundAddress = false;
            
            if (paragraphs && paragraphs.length > 0) {
              for (let j = 0; j < paragraphs.length; j++) {
                const p = paragraphs[j];
                const text = p.textContent;
                
                // Check if text looks like an address (contains postal code patterns or address keywords)
                if (text && (
                    text.match(/\\d+\\s+[\\w\\s]+\\s+St(\\.|reet)/i) || // Street pattern
                    text.match(/\\d+\\s+[\\w\\s]+\\s+(Ave|Avenue|Rd|Road|Ln|Lane|Dr|Drive|Blvd|Boulevard)/i) || // Road pattern
                    text.match(/[A-Z]{2}\\s+\\d{5}/) || // US state + zip
                    text.match(/[A-Z][0-9][A-Z]\\s?[0-9][A-Z][0-9]/)) // Canadian postal code
                ) {
                  officeLocations.push(cleanText(text));
                  foundAddress = true;
                }
              }
            }
            
            // If no address found in paragraphs, check if the element itself contains address-like text
            if (!foundAddress && el.textContent) {
              const text = el.textContent;
              
              // Extract potential address text but limit the length to avoid capturing too much
              if (text.match(/\\d+\\s+[\\w\\s]+\\s+(St|Street|Ave|Avenue|Rd|Road|Ln|Lane|Dr|Drive|Blvd|Boulevard)/i) ||
                  text.match(/[A-Z]{2}\\s+\\d{5}/) ||
                  text.match(/[A-Z][0-9][A-Z]\\s?[0-9][A-Z][0-9]/)) {
                
                // Limit the text to avoid getting too much content
                const cleanedText = cleanText(text);
                if (cleanedText.length < 200) { // Only use if it's reasonably short
                  officeLocations.push(cleanedText);
                }
              }
            }
          }
        }
        
        // Remove duplicates and clean up
        const uniqueLocations = [];
        const seenLocations = new Set();
        
        for (const location of officeLocations) {
          const normalizedLocation = location.toLowerCase();
          if (!seenLocations.has(normalizedLocation)) {
            seenLocations.add(normalizedLocation);
            uniqueLocations.push(location);
          }
        }
        
        if (uniqueLocations.length > 0) {
          aboutInfo.officeLocations = uniqueLocations;
          aboutInfo.globalPresence = uniqueLocations.length > 1;
        }
        
        // Extract industries
        try {
          const industries = new Set();
          const industryKeywords = [
            'technology', 'software', 'finance', 'healthcare', 'retail',
            'education', 'manufacturing', 'media', 'real estate', 'travel',
            'hospitality', 'automotive', 'legal', 'construction', 'energy',
            'consumer goods', 'telecommunications', 'transportation'
          ];
          
          // Look for explicit industry mentions
          const textElements = document.querySelectorAll('p, li, h2, h3, h4');
          for (let i = 0; i < textElements.length; i++) {
            const text = textElements[i].textContent.toLowerCase();
            
            // Check for direct industry mentions
            if (text.includes('industr') || text.includes('sector') || text.includes('speciali') || 
                text.includes('we serve') || text.includes('we work with') || text.includes('expertise in')) {
              industryKeywords.forEach(function(keyword) {
                if (text.includes(keyword)) {
                  industries.add(keyword.charAt(0).toUpperCase() + keyword.slice(1)); // Capitalize first letter
                }
              });
            }
            
            // Check for services provided to specific industries
            if ((text.includes('service') || text.includes('solution')) &&
                (text.match(/\\bfor\\s+[a-z\\s]+(industries|companies|businesses|firms)\\b/i))) {
              industryKeywords.forEach(function(keyword) {
                if (text.includes(keyword)) {
                  industries.add(keyword.charAt(0).toUpperCase() + keyword.slice(1));
                }
              });
            }
          }
          
          // Also scan for main services that might indicate industries served
          const serviceElements = document.querySelectorAll('a');
          for (let i = 0; i < serviceElements.length; i++) {
            const text = serviceElements[i].textContent.toLowerCase();
            industryKeywords.forEach(function(keyword) {
              if (text.includes(keyword) && text.includes('service')) {
                industries.add(keyword.charAt(0).toUpperCase() + keyword.slice(1));
              }
            });
          }
          
          if (industries.size > 0) {
            aboutInfo.industries = Array.from(industries);
          }
        } catch (industryError) {
          logger.error('Error extracting industries:', industryError);
        }
        
        // Extract company certifications
        try {
          // Define common certification patterns and keywords
          const certificationKeywords = [
            // ISO Standards
            'ISO ', 'ISO-', 'ISO/', // Base ISO prefix
            'ISO 9001', 'ISO 14001', 'ISO 27001', 'ISO 45001', 'ISO 13485', 'ISO 22000', 
            'ISO 20000', 'ISO 17025', 'ISO 31000', 'ISO 50001', 'ISO/IEC',
            
            // Security and Privacy
            'SOC 1', 'SOC 2', 'SOC 3', 'SSAE 16', 'SSAE 18',
            'GDPR', 'HIPAA', 'PCI DSS', 'CCPA', 'PIPEDA',
            
            // Quality and Management
            'CMMI', 'Six Sigma', 'Lean Six Sigma', 'TQM',
          ];
          
          // Define regex patterns for certification extraction
          // Using regex literals to avoid syntax issues
          const certRegexPatterns = [
            /ISO\\s*[0-9]+/i,
            /ISO\\/IEC\\s*[0-9]+/i,
            /SOC\\s*[123]/i,
            /SSAE\\s*[0-9]+/i,
            /CMMI\\s*(Level\\s*)?[1-5]/i
          ];
          
          // Places to look for certifications
          const potentialCertSections = document.querySelectorAll(
            'footer, .footer, #footer, ' +
            '[class*="certification"], [id*="certification"], ' +
            '[class*="certificate"], [id*="certificate"], ' +
            '[class*="compliance"], [id*="compliance"], ' +
            '[class*="quality"], [id*="quality"], ' +
            '.about, #about, [class*="about"], [id*="about"]'
          );
          
          const certificationHeadings = getElementsByText('h1, h2, h3, h4, h5, h6', 'certification');
          certificationHeadings.push.apply(certificationHeadings, getElementsByText('h1, h2, h3, h4, h5, h6', 'compliance'));
          certificationHeadings.push.apply(certificationHeadings, getElementsByText('h1, h2, h3, h4, h5, h6', 'standard'));
          certificationHeadings.push.apply(certificationHeadings, getElementsByText('h1, h2, h3, h4, h5, h6', 'quality'));
          certificationHeadings.push.apply(certificationHeadings, getElementsByText('h1, h2, h3, h4, h5, h6', 'accreditation'));
          
          // Add the sections containing certification headings
          const sectionsToSearch = Array.from(potentialCertSections);
          certificationHeadings.forEach(function(heading) {
            if (heading.parentElement) {
              sectionsToSearch.push(heading.parentElement);
              
              // Also look at next sibling as it often contains the list
              if (heading.nextElementSibling) {
                sectionsToSearch.push(heading.nextElementSibling);
              }
            }
          });
          
          // Extract potential certifications
          const foundCertifications = [];
          
          // First look for list items that might contain certifications
          sectionsToSearch.forEach(function(section) {
            // Look for list items
            const listItems = section.querySelectorAll('li');
            if (listItems && listItems.length > 0) {
              listItems.forEach(function(li) {
                const text = li.textContent ? li.textContent.trim() : '';
                
                // Check each certification pattern
                for (let i = 0; i < certRegexPatterns.length; i++) {
                  const matches = text.match(certRegexPatterns[i]);
                  if (matches) {
                    matches.forEach(function(match) {
                      // Clean up the match
                      const certification = match.trim().replace(/\\s+/g, ' ');
                      if (certification.length > 3 && certification.length < 50) {
                        foundCertifications.push(certification);
                      }
                    });
                  }
                }
              });
            }
            
            // Also check all text nodes for certification patterns
            const allText = section.textContent || '';
            for (let i = 0; i < certificationKeywords.length; i++) {
              const keyword = certificationKeywords[i];
              if (allText.toLowerCase().includes(keyword.toLowerCase())) {
                // Extract a reasonable context around the keyword (up to 50 chars)
                const keywordIndex = allText.toLowerCase().indexOf(keyword.toLowerCase());
                const start = Math.max(0, keywordIndex - 20);
                const end = Math.min(allText.length, keywordIndex + keyword.length + 30);
                const certText = allText.substring(start, end).trim();
                
                // Only add if it's a reasonable length and not already in the list
                if (certText.length > 3 && certText.length < 80 && 
                    !foundCertifications.some(c => c.includes(keyword))) {
                  foundCertifications.push(certText);
                }
              }
            }
            
            // Check for images with certification-related alt text
            const images = section.querySelectorAll('img');
            images.forEach(function(img) {
              const alt = img.getAttribute('alt') || '';
              const src = img.getAttribute('src') || '';
              
              // Check alt text and src for certification keywords
              for (let i = 0; i < certificationKeywords.length; i++) {
                const keyword = certificationKeywords[i].toLowerCase();
                if (alt.toLowerCase().includes(keyword) || src.toLowerCase().includes(keyword)) {
                  const imgText = alt || src.split('/').pop() || '';
                  if (imgText.length > 3) {
                    foundCertifications.push(imgText.trim());
                  }
                }
              }
            });
          });
          
          // Also search the entire document for strong certification patterns
          // (SOC, etc.) as they might be mentioned anywhere
          const strongCertPatterns = [
            'SOC', 'SSAE', 'CMMI', 'PCI DSS', 'HIPAA', 'GDPR'
          ];
          
          const allDocText = document.body.textContent || '';
          for (let i = 0; i < strongCertPatterns.length; i++) {
            const pattern = strongCertPatterns[i];
            if (allDocText.indexOf(pattern) !== -1) {
              // Find pattern occurrences and extract surrounding context
              let startPos = 0;
              while ((startPos = allDocText.indexOf(pattern, startPos)) !== -1) {
                // Extract context (20 chars before and after)
                const contextStart = Math.max(0, startPos - 20);
                const contextEnd = Math.min(allDocText.length, startPos + pattern.length + 20);
                const context = allDocText.substring(contextStart, contextEnd);
                
                // Clean up the extracted context
                const cleanContext = context.trim().replace(/\\s+/g, ' ');
                foundCertifications.push(cleanContext);
                
                // Move to next potential occurrence
                startPos = startPos + pattern.length;
              }
            }
          }
          
          // Remove duplicates and standardize
          if (foundCertifications.length > 0) {
            const uniqueCertifications = [];
            const seenCerts = {};
            
            foundCertifications.forEach(function(cert) {
              // Normalize the certification text
              const normalizedCert = cert.trim().replace(/\\s+/g, ' ');
              
              // Check if we've already seen this certification
              if (!seenCerts[normalizedCert.toLowerCase()]) {
                uniqueCertifications.push(normalizedCert);
                seenCerts[normalizedCert.toLowerCase()] = true;
              }
            });
            
            aboutInfo.certifications = uniqueCertifications;
          }
        } catch (certError) {
          logger.error('Error extracting certifications:', certError);
        }
        
        // Extract global presence and office locations from contact sections or footer
        try {
          // Look for address containers in footer, contact sections, and other common locations
          const addressContainers = [];
          
          // Find potential address containers by selector
          const footerElements = document.querySelectorAll('footer');
          const contactSections = document.querySelectorAll('.contact, #contact, [id*="contact"], [class*="contact"], .address, [class*="address"], .location, [class*="location"]');
          const addressElements = document.querySelectorAll('address, .address, [itemprop="address"], [property="address"], [typeof="PostalAddress"]');
          
          // Helper function to check if text might be an address
          function looksLikeAddress(text) {
            // Presence of zip/postal code patterns
            const hasPostalCode = /\b\\d{5}(-\\d{4})?\\b|\\b[A-Z]\\d[A-Z]\\s?\\d[A-Z]\\d\b|\b[A-Z]{1,2}\\d{1,2}[A-Z]?\\s\\d[A-Z]{2}\b/.test(text);
            
            // Check for city, state/province patterns
            const hasCityState = /\b[A-Z][a-zA-Z\\s]+,\\s*[A-Z]{2}\b/.test(text);
            
            // Check for international patterns (multiple words with commas and at least one number)
            const hasInternationalFormat = /[A-Za-z\\s]+,.*\\d+.*/.test(text);
            
            // Common address words
            const hasAddressKeywords = /(street|avenue|boulevard|road|drive|lane|place|court|way|st\\.|ave\\.|blvd\\.|rd\\.|dr\\.|ln\\.|pl\\.|ct\\.|floor|suite|unit|building|bldg)\b/i.test(text);
            
            // Either has postal code, city/state pattern, or address keywords with numbers
            return (hasPostalCode || hasCityState || (hasAddressKeywords && /\\d+/.test(text)) || hasInternationalFormat);
          }
          
          // Add all potential containers to our list
          footerElements.forEach(function(el) { addressContainers.push(el); });
          contactSections.forEach(function(el) { addressContainers.push(el); });
          addressElements.forEach(function(el) { addressContainers.push(el); });
          
          // Also search for headings that might indicate office locations
          const locationHeadings = getElementsByText('h1, h2, h3, h4, h5, h6', 'office');
          locationHeadings.push.apply(locationHeadings, getElementsByText('h1, h2, h3, h4, h5, h6', 'location'));
          locationHeadings.push.apply(locationHeadings, getElementsByText('h1, h2, h3, h4, h5, h6', 'address'));
          locationHeadings.push.apply(locationHeadings, getElementsByText('h1, h2, h3, h4, h5, h6', 'headquarter'));
          locationHeadings.push.apply(locationHeadings, getElementsByText('h1, h2, h3, h4, h5, h6', 'global'));
          locationHeadings.push.apply(locationHeadings, getElementsByText('h1, h2, h3, h4, h5, h6', 'presence'));
          
          // Add the parent container of each heading
          locationHeadings.forEach(function(heading) {
            if (heading.parentElement) {
              addressContainers.push(heading.parentElement);
              
              // Also look at next sibling as it might contain the address list
              if (heading.nextElementSibling) {
                addressContainers.push(heading.nextElementSibling);
              }
            }
          });
          
          // Extract potential addresses from containers
          const potentialAddresses = [];
          
          // Different approaches to finding addresses
          addressContainers.forEach(function(container) {
            // Look for address elements within container
            const addressEls = container.querySelectorAll('address, [itemprop="address"], [property="address"], [typeof="PostalAddress"]');
            if (addressEls && addressEls.length > 0) {
              addressEls.forEach(function(addrEl) {
                if (addrEl.textContent && addrEl.textContent.trim().length > 10) {
                  potentialAddresses.push(addrEl.textContent.trim());
                }
              });
            }
            
            // Look for list items that might be addresses
            const listItems = container.querySelectorAll('li');
            if (listItems && listItems.length > 0) {
              listItems.forEach(function(li) {
                const text = li.textContent ? li.textContent.trim() : '';
                if (text.length > 10 && looksLikeAddress(text)) {
                  potentialAddresses.push(text);
                }
              });
            }
            
            // Look for divs or paragraphs that might contain addresses
            const textBlocks = container.querySelectorAll('div, p, span');
            textBlocks.forEach(function(block) {
              const text = block.textContent ? block.textContent.trim() : '';
              // Only consider text blocks that look like addresses and are reasonably sized
              if (text.length > 10 && text.length < 200 && looksLikeAddress(text)) {
                potentialAddresses.push(text);
              }
            });
          });
          
          // Process and clean up addresses
          const cleanedAddresses = [];
          potentialAddresses.forEach(function(addr) {
            // Remove excess whitespace
            let cleaned = addr.replace(/\\s+/g, ' ').trim();
            
            // Add if not already included and not too short
            if (cleaned.length > 10 && !cleanedAddresses.includes(cleaned)) {
              cleanedAddresses.push(cleaned);
            }
          });
          
          // Set global presence flag and office locations if we found multiple addresses
          if (cleanedAddresses.length >= 2) {
            aboutInfo.globalPresence = true;
            aboutInfo.officeLocations = cleanedAddresses;
          } else if (cleanedAddresses.length === 1) {
            aboutInfo.globalPresence = false;
            aboutInfo.officeLocations = cleanedAddresses;
          }
        } catch (locationError) {
          logger.error('Error extracting global presence:', locationError);
        }
        
        return aboutInfo;
      })()
    `);

		// Cast the result to AboutInfo type
		return result as AboutInfo;
	} catch (error) {
		logger.error("Error extracting about info:", error);
		return {};
	}
}

/**
 * Extracts both about and team information from a page
 * @param page - Puppeteer page instance
 * @returns Promise containing company info
 */
export async function extractAboutAndTeamInfo(
	page: Page,
): Promise<CompanyInfo> {
	logger.info("Extracting about and team information from current page only");

	try {
		// Extract info from the current page only - no navigation
		const aboutInfo = await extractAboutInfo(page);

		// Get team information
		const teamMembers = await extractTeamInfo(page);

		// Return combined info - just merge the about info with team members
		return {
			...aboutInfo,
			teamMembers,
		};
	} catch (error) {
		logger.error("Error in extractAboutAndTeamInfo:", error);
		return {
			companyDescription: undefined,
			foundingInfo: undefined,
			missionStatement: undefined,
			teamMembers: undefined,
			companyValues: undefined,
			awards: undefined,
			industries: undefined,
			globalPresence: undefined,
			officeLocations: undefined,
			certifications: undefined,
		};
	}
}

/**
 * Finds about and team pages on a website
 * @param page - Puppeteer page instance
 * @param baseUrl - Base URL of the website
 * @param nestedLinks - Optional array of links to check
 * @returns Promise containing array of relevant page URLs
 */
/**
 * Checks if about details are complete enough to be considered done
 * @param info - Company information object
 * @returns boolean indicating if information is complete
 */
export function isAboutDetailsComplete(info: CompanyInfo): boolean {
	// Check if we have the essential about page information
	// We consider it complete if we have at least 3 out of these 5 key pieces of information
	let completenessScore = 0;

	if (info.companyDescription && info.companyDescription.length > 100) {
		completenessScore += 1;
	}

	if (info.foundingInfo && info.foundingInfo.length > 10) {
		completenessScore += 1;
	}

	if (info.missionStatement && info.missionStatement.length > 30) {
		completenessScore += 1;
	}

	if (info.companyValues && info.companyValues.length >= 2) {
		completenessScore += 1;
	}

	if (info.industries && info.industries.length >= 1) {
		completenessScore += 1;
	}

	// Check team info completeness if it exists
	const hasTeamInfo = info.teamMembers && info.teamMembers.length > 0;
	const teamInfoComplete = hasTeamInfo
		? isTeamInfoComplete(info.teamMembers)
		: false;

	// Either very complete about info OR decent about info + team info
	return (
		completenessScore >= 3 || (completenessScore >= 2 && teamInfoComplete)
	);
}

/**
 * Finds about and team pages on a website
 * @param page - Puppeteer page instance
 * @param baseUrl - Base URL of the website
 * @param nestedLinks - Optional array of links to check
 * @returns Promise containing array of relevant page URLs
 */
export async function findAboutAndTeamPages(
	page: Page,
	baseUrl: string,
	nestedLinks?: string[],
): Promise<string[]> {
	logger.info(`Finding about and team pages from: ${baseUrl}`);

	try {
		// ULTRA SIMPLE APPROACH: Pass a direct function to page.evaluate with minimal complexity
		const aboutPageLinks = await page.evaluate((baseUrl) => {
			try {
				// Local variables inside browser context
				const aboutPageUrls: any = [];
				const seenUrls: { [key: string]: boolean } = {};
				const excludePatterns = [
					"/contact",
					"/privacy",
					"/terms",
					"/book-a-call",
					"/free-website-analysis",
					"/free-quote",
					"/get-quote",
					"/book-demo",
					"/services",
					"/cart",
					"/blog",
					"/news",
					"/reviews",
					"/testimonials",
					"/case-studies",
					"/portfolio",
					"/shop",
					"/store",
					"/faq",
					"/support",
					"/help",
					"/join-us",
					"/login",
					"/register",
					"/signin",
					"/signup",
					"/schedule-",
				];
				const aboutKeywords = [
					"about",
					"about-us",
					"about us",
					"our-story",
					"our story",
					"who-we-are",
					"who we are",
					"team",
					"our-team",
					"our team",
					"meet-the-team",
					"meet the team",
					"meet-our-team",
					"meet our team",
					"leadership",
					"company",
					"company-profile",
					"company profile",
					"life",
					"staff",
					"history",
				];

				// Add a URL if it's not seen before
				function addUrlIfUnique(href: string) {
					if (!href) return false;

					try {
						const url = new URL(href);
						const normalized =
							url.origin +
							url.pathname.replace(/\/$/, "") +
							url.search;

						if (seenUrls[normalized]) return false;
						seenUrls[normalized] = true;
						aboutPageUrls.push(href);
						// Don't use logger in browser context
						return true;
					} catch (e) {
						console.log("Error parsing URL:", e);
						return false;
					}
				}

				// Check if a path should be excluded
				function shouldExclude(path: string) {
					for (const pattern of excludePatterns) {
						if (path.includes(pattern)) return true;
					}
					return false;
				}

				// Get links and base URL
				const links = document.querySelectorAll("a");
				const baseUrlObj = new URL(baseUrl);
				// Don't use logger in browser context

				// Process each link
				for (
					let i = 0;
					i < links.length && aboutPageUrls.length < 3;
					i++
				) {
					const link = links[i];
					const href = link?.href;
					const text = (link?.textContent || "").toLowerCase();

					// Skip invalid links
					if (
						!href ||
						href.startsWith("javascript:") ||
						href.startsWith("#") ||
						href.startsWith("tel:") ||
						href.startsWith("mailto:")
					) {
						continue;
					}

					try {
						// Check if same domain
						const url = new URL(href);
						if (url.hostname !== baseUrlObj.hostname) continue;

						// Check for exclusion patterns
						const path = url.pathname.toLowerCase();
						if (shouldExclude(path)) continue;

						// Check for about keywords
						for (const keyword of aboutKeywords) {
							if (
								path.includes(keyword) ||
								text.includes(keyword)
							) {
								addUrlIfUnique(href);
								break;
							}
						}
					} catch (e) {
						console.log("Error parsing URL:", e);
						// Skip invalid URLs
						continue;
					}
				}

				// Don't use logger in browser context
				return aboutPageUrls;
			} catch (e) {
				console.log("Error in findAboutAndTeamPages:", e);
				// Don't use logger in browser context
				return [];
			}
		}, baseUrl);

		// Return immediately if we found about pages from DOM
		if (
			aboutPageLinks &&
			Array.isArray(aboutPageLinks) &&
			aboutPageLinks.length > 0
		) {
			logger.info(
				`Using about pages found in DOM: ${aboutPageLinks.length}`,
			);
			return aboutPageLinks;
		}

		// Handle case for nested links if provided and no about pages found from DOM
		if (nestedLinks && nestedLinks.length > 0) {
			logger.info(
				"No about pages found in DOM, falling back to nested links...",
			);

			// Process nested links on server side to avoid browser context issues
			const uniqueLinks = new Set<string>();
			const baseUrlObj = new URL(baseUrl);
			const excludePatterns = [
				"/contact",
				"/privacy",
				"/terms",
				"/book-a-call",
				"/free-website-analysis",
				"/free-quote",
				"/get-quote",
				"/book-demo",
				"/services",
				"/cart",
				"/blog",
				"/news",
				"/reviews",
				"/testimonials",
				"/case-studies",
				"/portfolio",
				"/shop",
				"/store",
				"/faq",
				"/support",
				"/help",
				"/join-us",
				"/login",
				"/register",
				"/signin",
				"/signup",
				"/schedule-",
			];

			for (const link of nestedLinks) {
				try {
					const url = new URL(link, baseUrl);

					// Only include links from the same domain
					if (url.hostname !== baseUrlObj.hostname) {
						continue;
					}

					const path = url.pathname.toLowerCase();
					const hasAboutKeyword =
						path.includes("/about") ||
						path.includes("/team") ||
						path.includes("/who-we-are") ||
						path.includes("/company") ||
						path.includes("/leadership") ||
						path.includes("/management") ||
						path.includes("/mission") ||
						path.includes("/values");

					// Skip if doesn't have an about keyword
					if (!hasAboutKeyword) continue;

					// Check if the URL should be excluded
					const shouldExclude = excludePatterns.some((pattern) =>
						path.includes(pattern),
					);

					// Skip if URL should be excluded
					if (shouldExclude) {
						logger.debug(
							`Excluding nested link by pattern: ${link}`,
						);
						continue;
					}

					// Normalize URL before adding
					const normalizedUrl =
						url.origin +
						url.pathname.replace(/\/$/, "") +
						url.search;

					// Add to unique links if not already present
					if (!uniqueLinks.has(normalizedUrl)) {
						uniqueLinks.add(normalizedUrl);
						logger.debug(
							`Added unique nested link: ${normalizedUrl}`,
						);
					}
				} catch (error) {
					logger.error("Error processing nested link:", link, error);
				}
			}

			// Convert unique links set to array, limited to 3
			const result = Array.from(uniqueLinks).slice(0, 3);
			logger.info(
				`Found ${result.length} unique about/team pages from nested links`,
			);
			return result;
		}

		// Return empty array if no about/team pages found
		return [];
	} catch (error) {
		logger.error("Error finding about and team pages:", error);
		return [];
	}
}

/**
 * Merges two company information objects into one.
 *
 * @param existing - Existing company information
 * @param additional - Additional company information to merge
 * @returns Merged company information
 */
export function mergeCompanyInfo(
	existing: CompanyInfo,
	additional: CompanyInfo,
): CompanyInfo {
	const merged: CompanyInfo = { ...existing };

	// Use additional company description if existing one is missing or shorter
	if (additional.companyDescription) {
		if (
			!existing.companyDescription ||
			additional.companyDescription.length >
				existing.companyDescription.length
		) {
			merged.companyDescription = additional.companyDescription;
		}
	}

	// Use additional founding info if existing one is missing
	if (!existing.foundingInfo && additional.foundingInfo) {
		merged.foundingInfo = additional.foundingInfo;
	}

	// Use additional mission statement if existing one is missing
	if (!existing.missionStatement && additional.missionStatement) {
		merged.missionStatement = additional.missionStatement;
	}

	// Merge team members using the specialized team merger function
	merged.teamMembers = mergeTeamInfo(
		existing.teamMembers,
		additional.teamMembers,
	);

	// Merge company values
	if (additional.companyValues && additional.companyValues.length > 0) {
		merged.companyValues = Array.from(
			new Set([
				...(existing.companyValues || []),
				...additional.companyValues,
			]),
		);
	}

	// Merge awards
	if (additional.awards && additional.awards.length > 0) {
		merged.awards = Array.from(
			new Set([...(existing.awards || []), ...additional.awards]),
		);
	}

	// Merge industries
	if (additional.industries && additional.industries.length > 0) {
		merged.industries = Array.from(
			new Set([...(existing.industries || []), ...additional.industries]),
		);
	}

	return merged;
}

/**
 * Configuration for company info extraction
 */
interface ExtractCompanyInfoConfig {
	maxPagesToVisit?: number;
	timeout?: number;
}

/**
 * Extracts company information from a website by visiting about/team pages
 * @param page - Puppeteer page instance
 * @param initialUrl - The initial URL of the website
 * @param aboutPages - Array of about/team page URLs to visit
 * @param config - Configuration options
 * @returns Promise containing company information
 */
export async function extractCompanyInfoFromSite(
	page: Page,
	initialUrl: string,
	aboutPages: string[],
	config: ExtractCompanyInfoConfig = { maxPagesToVisit: 3, timeout: 30000 },
): Promise<CompanyInfo> {
	logger.info(`Extracting company info from ${aboutPages.length} pages`);

	// Initialize result
	let companyInfo: CompanyInfo = {};

	// Limit the number of pages to visit
	const pagesToVisit = aboutPages.slice(0, config.maxPagesToVisit || 3);

	// Visit each page and extract information
	for (const pageUrl of pagesToVisit) {
		try {
			// Navigate to the page
			await page.goto(pageUrl, {
				waitUntil: "networkidle2",
				timeout: config.timeout || 30000,
			});

			logger.info(`Extracting about info from: ${pageUrl}`);
			// Extract about info
			const aboutInfo = await extractAboutInfo(page);

			// Merge with existing info
			companyInfo = mergeCompanyInfo(companyInfo, aboutInfo);

			// Check if we have enough information to stop early
			if (isAboutDetailsComplete(companyInfo)) {
				logger.info(
					"Collected complete company information, stopping early",
				);
				break;
			}
		} catch (error: any) {
			logger.error(`Error extracting from ${pageUrl}: ${error.message}`);
			// Continue with next page on error
		}
	}

	return companyInfo;
}
